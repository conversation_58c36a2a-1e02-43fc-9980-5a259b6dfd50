"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader, DataTable, StatusBadge, PaymentStatusBadge } from '@/components/admin';
import { getOrders, getPaymentsByOrderId, isUserAdmin } from '@/features/admin/api';
import { AdminOrder, OrderListParams } from '@/features/admin/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Edit, Eye, Package, CreditCard } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatCurrency } from '@/lib/utils';
import { createSPASassClient } from '@/lib/supabase/client';

export default function OrdersPage() {
  const [orders, setOrders] = useState<AdminOrder[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<string>('all');
  const [params, setParams] = useState<OrderListParams>({
    page: 1,
    per_page: 10,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  useEffect(() => {
    const checkAdminStatus = async () => {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const adminStatus = await isUserAdmin(user.id);
        setIsAdmin(adminStatus);
      }
    };
    checkAdminStatus();
  }, []);

  const fetchOrders = async (params: OrderListParams) => {
    setLoading(true);
    setError(null);
    try {
      console.log('Fetching orders with params:', params);
      const { orders, count } = await getOrders(params);
      console.log('Raw API response:', { orders, count });

      if (!Array.isArray(orders)) {
        console.error('Orders is not an array:', orders);
        setError('Invalid response format from server');
        setOrders([]);
        setTotalCount(0);
        return;
      }

      // Fetch payment information for each order
      const ordersWithPayments = await Promise.all(
        orders.map(async (order) => {
          const payments = await getPaymentsByOrderId(order.id);
          const latestPayment = payments[0]; // Get the most recent payment
          return {
            ...order,
            payments,
            payment_status: latestPayment?.payment_status || 'no_payment',
            payment_method: latestPayment?.payment_method || '',
            transaction_id: latestPayment?.transaction_id || '',
          };
        })
      );

      // Filter by payment status if needed
      const filteredOrders = paymentStatusFilter === 'all'
        ? ordersWithPayments
        : ordersWithPayments.filter(order => order.payment_status === paymentStatusFilter);

      setOrders(filteredOrders);
      setTotalCount(count);
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch orders');
      setOrders([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders(params);
  }, [params, paymentStatusFilter]);

  const handlePageChange = (page: number) => {
    setParams({ ...params, page });
  };

  const handleSearch = (search: string) => {
    setParams({ ...params, page: 1, search });
  };

  const handleSortChange = (sort_by: string, sort_order: 'asc' | 'desc') => {
    setParams({ ...params, sort_by, sort_order });
  };

  const handleStatusFilter = (status: string) => {
    setParams({
      ...params,
      page: 1,
      status: status === 'all' ? undefined : status,
    });
  };

  const handlePaymentStatusFilter = (status: string) => {
    setPaymentStatusFilter(status);
  };

  const columns = [
    {
      key: 'id',
      header: 'Order ID',
      cell: (order: AdminOrder) => (
        <div className="font-medium text-primary-600">
          {order.id.substring(0, 8)}...
        </div>
      ),
      sortable: true,
    },
    {
      key: 'user_email',
      header: 'Customer',
      cell: (order: AdminOrder) => (
        <div>{order.user_email || order.shipping_email || order.shipping_name || 'N/A'}</div>
      ),
      sortable: true,
    },
    {
      key: 'total',
      header: 'Total',
      cell: (order: AdminOrder) => (
        <div className="font-medium">
          {formatCurrency(order.total, order.currency)}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'status',
      header: 'Order Status',
      cell: (order: AdminOrder) => <StatusBadge status={order.status} />,
      sortable: true,
    },
    {
      key: 'payment_status',
      header: 'Payment Status',
      cell: (order: AdminOrder) => <PaymentStatusBadge status={order.payment_status || 'no_payment'} />,
      sortable: true,
    },
    {
      key: 'payment_method',
      header: 'Payment Method',
      cell: (order: AdminOrder) => (
        <div className="flex items-center gap-1">
          <CreditCard className="h-4 w-4 text-gray-400" />
          <span>{order.payment_method || '-'}</span>
        </div>
      ),
    },
    {
      key: 'items_count',
      header: 'Items',
      cell: (order: AdminOrder) => (
        <div className="flex items-center gap-1">
          <Package className="h-4 w-4 text-gray-400" />
          <span>{order.items_count || 0}</span>
        </div>
      ),
    },
    {
      key: 'created_at',
      header: 'Date',
      cell: (order: AdminOrder) => (
        <div>{new Date(order.created_at).toLocaleDateString()}</div>
      ),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (order: AdminOrder) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/orders/${order.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          {isAdmin && (
            <Link href={`/admin/orders/${order.id}/edit`}>
              <Button variant="ghost" size="sm">
                <Edit className="h-4 w-4" />
              </Button>
            </Link>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <AdminHeader
        title={isAdmin ? "Orders & Payments" : "Store Orders"}
        description={isAdmin ? "Manage your marketplace orders and payments" : "Manage your store orders"}
      />

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-wrap gap-4">
        <div className="w-full sm:w-auto">
          <Select
            value={params.status || 'all'}
            onValueChange={handleStatusFilter}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Order Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Order Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="shipped">Shipped</SelectItem>
              <SelectItem value="delivered">Delivered</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
              <SelectItem value="refunded">Refunded</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="w-full sm:w-auto">
          <Select
            value={paymentStatusFilter}
            onValueChange={handlePaymentStatusFilter}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Payment Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Payment Statuses</SelectItem>
              <SelectItem value="pending">Pending Payment</SelectItem>
              <SelectItem value="completed">Paid</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
              <SelectItem value="no_payment">No Payment</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={orders}
        totalCount={totalCount}
        pageSize={params.per_page || 10}
        currentPage={params.page || 1}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        searchPlaceholder="Search orders..."
        onSortChange={handleSortChange}
        sortKey={params.sort_by}
        sortOrder={params.sort_order}
        isLoading={loading}
      />
    </div>
  );
}
