import React from 'react';

interface PaymentStatusBadgeProps {
    status: string;
}

export default function PaymentStatusBadge({ status }: PaymentStatusBadgeProps) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'failed':
                return 'bg-red-100 text-red-800';
            case 'no_payment':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed':
                return 'Paid';
            case 'pending':
                return 'Pending';
            case 'failed':
                return 'Failed';
            case 'no_payment':
                return 'No Payment';
            default:
                return status;
        }
    };

    return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
            {getStatusText(status)}
        </span>
    );
} 